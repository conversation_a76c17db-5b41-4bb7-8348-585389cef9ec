import { supabase } from "@/integrations/supabase/client";
import { sendNotificationEmail } from "@/lib/utils";

export interface CreateNotificationParams {
  userId: string;
  title: string;
  message: string;
  type?: "info" | "success" | "warning" | "error";
  actionUrl?: string;
  metadata?: Record<string, any>;
}

export interface NotificationTemplate {
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  actionUrl?: string;
}

/**
 * Create a new notification for a user
 */
export const createNotification = async (
  params: CreateNotificationParams
): Promise<string | null> => {
  try {
    const { data, error } = await (supabase as any)
      .from("notifications")
      .insert({
        user_id: params.userId,
        title: params.title,
        message: params.message,
        type: params.type || "info",
        action_url: params.actionUrl || null,
        metadata: params.metadata || {},
      })
      .select("id")
      .single();

    if (error) {
      console.error("Error creating notification:", error);
      return null;
    }

    return data?.id || null;
  } catch (error) {
    console.error("Error creating notification:", error);
    return null;
  }
};

/**
 * Create notifications for multiple users
 */
export const createBulkNotifications = async (
  userIds: string[],
  template: NotificationTemplate
): Promise<number> => {
  try {
    const promises = userIds.map((userId) =>
      createNotification({
        userId,
        title: template.title,
        message: template.message,
        type: template.type,
        actionUrl: template.actionUrl,
      })
    );

    const results = await Promise.all(promises);
    const successCount = results.filter((result) => result !== null).length;

    return successCount;
  } catch (error) {
    console.error("Error creating bulk notifications:", error);
    return 0;
  }
};

/**
 * Notification templates for common scenarios
 */
export const NotificationTemplates = {
  // Job-related notifications
  newJobAvailable: (jobTitle: string, jobId: string): NotificationTemplate => ({
    title: "Nieuwe klus beschikbaar",
    message: `Er is een nieuwe klus beschikbaar die past bij jouw diensten: ${jobTitle}`,
    type: "info",
    actionUrl: `/banen/${jobId}`,
  }),

  jobResponseReceived: (jobTitle: string): NotificationTemplate => ({
    title: "Nieuwe reactie ontvangen",
    message: `Je hebt een nieuwe reactie ontvangen op je klus '${jobTitle}'`,
    type: "success",
    actionUrl: "/gesprekken",
  }),

  jobCompleted: (jobTitle: string): NotificationTemplate => ({
    title: "Klus voltooid",
    message: `De klus '${jobTitle}' is succesvol voltooid`,
    type: "success",
    actionUrl: "/banen",
  }),

  jobCancelled: (jobTitle: string): NotificationTemplate => ({
    title: "Klus geannuleerd",
    message: `De klus '${jobTitle}' is geannuleerd`,
    type: "warning",
    actionUrl: "/banen",
  }),

  // Profile-related notifications
  profileIncomplete: (): NotificationTemplate => ({
    title: "Profiel incompleet",
    message:
      "Je profiel is nog niet compleet. Voeg meer informatie toe om meer klussen te ontvangen.",
    type: "warning",
    actionUrl: "/profiel",
  }),

  profileApproved: (): NotificationTemplate => ({
    title: "Profiel goedgekeurd",
    message: "Je profiel is goedgekeurd en je kunt nu klussen ontvangen!",
    type: "success",
    actionUrl: "/profiel",
  }),

  profileRejected: (reason?: string): NotificationTemplate => ({
    title: "Profiel afgekeurd",
    message: `Je profiel is afgekeurd. ${
      reason ? `Reden: ${reason}` : "Controleer je gegevens en probeer opnieuw."
    }`,
    type: "error",
    actionUrl: "/profiel",
  }),

  // Balance-related notifications
  balanceUpdated: (newBalance: number): NotificationTemplate => ({
    title: "Saldo bijgewerkt",
    message: `Je saldo is bijgewerkt. Nieuw saldo: €${newBalance.toFixed(2)}`,
    type: "success",
    actionUrl: "/evenwicht",
  }),

  lowBalance: (currentBalance: number): NotificationTemplate => ({
    title: "Laag saldo",
    message: `Je saldo is laag (€${currentBalance.toFixed(
      2
    )}). Voeg geld toe om te kunnen reageren op klussen.`,
    type: "warning",
    actionUrl: "/evenwicht",
  }),

  paymentReceived: (amount: number): NotificationTemplate => ({
    title: "Betaling ontvangen",
    message: `Je hebt een betaling van €${amount.toFixed(2)} ontvangen`,
    type: "success",
    actionUrl: "/evenwicht",
  }),

  // Message-related notifications
  newMessage: (senderName: string): NotificationTemplate => ({
    title: "Nieuw bericht",
    message: `Je hebt een nieuw bericht ontvangen van ${senderName}`,
    type: "info",
    actionUrl: "/gesprekken",
  }),

  // System notifications
  systemMaintenance: (
    startTime: string,
    endTime: string
  ): NotificationTemplate => ({
    title: "Systeem onderhoud",
    message: `Er vindt gepland onderhoud plaats van ${startTime} tot ${endTime}`,
    type: "info",
  }),

  welcomeMessage: (): NotificationTemplate => ({
    title: "Welkom bij Klusgebied!",
    message:
      "Bedankt voor je registratie. Vul je profiel aan om meer klussen te ontvangen.",
    type: "info",
    actionUrl: "/profiel",
  }),

  // Review notifications
  newReview: (rating: number): NotificationTemplate => ({
    title: "Nieuwe beoordeling",
    message: `Je hebt een nieuwe beoordeling ontvangen: ${rating} sterren`,
    type: "success",
    actionUrl: "/beoordelingen",
  }),

  reviewReminder: (jobTitle: string): NotificationTemplate => ({
    title: "Beoordeling gevraagd",
    message: `Vergeet niet om een beoordeling achter te laten voor de klus '${jobTitle}'`,
    type: "info",
    actionUrl: "/beoordelingen",
  }),
};

/**
 * Notify craftsmen about a new job
 */
export const notifyCraftsmenNewJob = async (
  jobId: string,
  jobTitle: string,
  services: string[]
): Promise<number> => {
  try {
    // Find craftsmen with matching services
    const { data: craftsmen, error: craftsmenError } = await supabase
      .from("profiles")
      .select("id")
      .eq("user_type", "vakman")
      .overlaps("services", services);

    if (craftsmenError) {
      console.error("Error fetching craftsmen:", craftsmenError);
      return 0;
    }

    if (!craftsmen || craftsmen.length === 0) {
      return 0;
    }

    // Create notifications for all matching craftsmen
    const notifications = craftsmen.map((craftsman) => ({
      user_id: craftsman.id,
      title: "Nieuwe klus beschikbaar",
      message: `Er is een nieuwe klus beschikbaar die past bij jouw diensten: ${jobTitle}`,
      type: "info" as const,
      action_url: `/banen/${jobId}`,
      metadata: {
        job_id: jobId,
        job_title: jobTitle,
        services: services,
      },
    }));

    const { error: insertError } = await (supabase as any)
      .from("notifications")
      .insert(notifications);

    if (insertError) {
      console.error("Error creating notifications:", insertError);
      return 0;
    }

    return notifications.length;
  } catch (error) {
    console.error("Error notifying craftsmen:", error);
    return 0;
  }
};

/**
 * Send welcome notification to new user
 */
export const sendWelcomeNotification = async (
  userId: string
): Promise<boolean> => {
  const notificationId = await createNotification({
    userId,
    ...NotificationTemplates.welcomeMessage(),
  });

  return notificationId !== null;
};

/**
 * Send profile incomplete notification
 */
export const sendProfileIncompleteNotification = async (
  userId: string
): Promise<boolean> => {
  const notificationId = await createNotification({
    userId,
    ...NotificationTemplates.profileIncomplete(),
  });

  return notificationId !== null;
};

/**
 * Send balance update notification
 */
export const sendBalanceUpdateNotification = async (
  userId: string,
  newBalance: number
): Promise<boolean> => {
  const notificationId = await createNotification({
    userId,
    ...NotificationTemplates.balanceUpdated(newBalance),
  });

  return notificationId !== null;
};

/**
 * Enhanced notification functions for common scenarios
 */

/**
 * Notify user about new job with email/SMS support
 */
export const notifyUserNewJob = async (
  userId: string,
  userEmail: string,
  userPhone: string | null,
  jobTitle: string,
  jobId: string,
  sendEmail: boolean = true,
  sendSMS: boolean = false
) => {
  return await sendEnhancedNotification({
    userId,
    userEmail,
    userPhone: userPhone || undefined,
    title: "Nieuwe klus beschikbaar",
    message: `Er is een nieuwe klus beschikbaar die past bij jouw diensten: ${jobTitle}`,
    type: "info",
    actionUrl: `/banen/${jobId}`,
    sendEmail,
    sendSMS,
    emailSubject: "Nieuwe klus beschikbaar op Klusgebied",
    emailContent: `
      <div style="font-family: Arial, sans-serif; color: #333;">
        <h2>Nieuwe klus beschikbaar!</h2>
        <p>Er is een nieuwe klus beschikbaar die past bij jouw diensten:</p>
        <h3>${jobTitle}</h3>
        <p><a href="${
          typeof window !== "undefined"
            ? window.location.origin
            : "https://klusgebied.nl"
        }/banen/${jobId}" style="background: #f1c40f; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Bekijk klus</a></p>
      </div>
    `,
    smsMessage: `Nieuwe klus: ${jobTitle}. Bekijk op klusgebied.nl`,
  });
};

/**
 * Notify user about job response with email/SMS support
 */
export const notifyUserJobResponse = async (
  userId: string,
  userEmail: string,
  userPhone: string | null,
  jobTitle: string,
  responderName: string,
  sendEmail: boolean = true,
  sendSMS: boolean = false
) => {
  return await sendEnhancedNotification({
    userId,
    userEmail,
    userPhone: userPhone || undefined,
    title: "Nieuwe reactie ontvangen",
    message: `${responderName} heeft gereageerd op je klus '${jobTitle}'`,
    type: "success",
    actionUrl: "/gesprekken",
    sendEmail,
    sendSMS,
    emailSubject: "Nieuwe reactie op je klus",
    emailContent: `
      <div style="font-family: Arial, sans-serif; color: #333;">
        <h2>Nieuwe reactie ontvangen!</h2>
        <p><strong>${responderName}</strong> heeft gereageerd op je klus:</p>
        <h3>${jobTitle}</h3>
        <p><a href="${
          typeof window !== "undefined"
            ? window.location.origin
            : "https://klusgebied.nl"
        }/gesprekken" style="background: #f1c40f; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Bekijk reactie</a></p>
      </div>
    `,
    smsMessage: `${responderName} heeft gereageerd op je klus '${jobTitle}'. Bekijk op klusgebied.nl`,
  });
};

/**
 * Notify user about balance update with email support
 */
export const notifyUserBalanceUpdate = async (
  userId: string,
  userEmail: string,
  newBalance: number,
  changeAmount: number,
  reason: string,
  sendEmail: boolean = true
) => {
  const isPositive = changeAmount > 0;
  return await sendEnhancedNotification({
    userId,
    userEmail,
    title: isPositive ? "Saldo verhoogd" : "Saldo verlaagd",
    message: `Je saldo is ${
      isPositive ? "verhoogd" : "verlaagd"
    } met €${Math.abs(changeAmount).toFixed(
      2
    )}. Nieuw saldo: €${newBalance.toFixed(2)}`,
    type: isPositive ? "success" : "info",
    actionUrl: "/evenwicht",
    sendEmail,
    sendSMS: false,
    emailSubject: `Saldo ${isPositive ? "verhoogd" : "verlaagd"} - Klusgebied`,
    emailContent: `
      <div style="font-family: Arial, sans-serif; color: #333;">
        <h2>Saldo ${isPositive ? "verhoogd" : "verlaagd"}</h2>
        <p>Je saldo is ${
          isPositive ? "verhoogd" : "verlaagd"
        } met <strong>€${Math.abs(changeAmount).toFixed(2)}</strong></p>
        <p><strong>Reden:</strong> ${reason}</p>
        <p><strong>Nieuw saldo:</strong> €${newBalance.toFixed(2)}</p>
        <p><a href="${
          typeof window !== "undefined"
            ? window.location.origin
            : "https://klusgebied.nl"
        }/evenwicht" style="background: #f1c40f; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Bekijk saldo</a></p>
      </div>
    `,
  });
};

/**
 * Send welcome notification to new user with email
 */
export const sendEnhancedWelcomeNotification = async (
  userId: string,
  userEmail: string,
  firstName: string,
  userType: string
) => {
  return await sendEnhancedNotification({
    userId,
    userEmail,
    title: "Welkom bij Klusgebied!",
    message:
      "Bedankt voor je registratie. Vul je profiel aan om meer klussen te ontvangen.",
    type: "info",
    actionUrl: "/profiel",
    sendEmail: true,
    sendSMS: false,
    emailSubject: "Welkom bij Klusgebied!",
    emailContent: `
      <div style="font-family: Arial, sans-serif; color: #333;">
        <h2>Welkom bij Klusgebied, ${firstName}!</h2>
        <p>Bedankt voor je registratie als ${
          userType === "vakman" ? "vakman" : "klusaanvrager"
        }.</p>
        <p>Om het meeste uit Klusgebied te halen, raden we je aan om:</p>
        <ul>
          <li>Je profiel volledig in te vullen</li>
          <li>Een profielfoto toe te voegen</li>
          ${
            userType === "vakman"
              ? "<li>Je diensten en vaardigheden toe te voegen</li>"
              : "<li>Je eerste klus te plaatsen</li>"
          }
        </ul>
        <p><a href="${
          typeof window !== "undefined"
            ? window.location.origin
            : "https://klusgebied.nl"
        }/profiel" style="background: #f1c40f; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Profiel aanvullen</a></p>
      </div>
    `,
  });
};

/**
 * Send new message notification
 */
export const sendNewMessageNotification = async (
  userId: string,
  senderName: string
): Promise<boolean> => {
  const notificationId = await createNotification({
    userId,
    ...NotificationTemplates.newMessage(senderName),
  });

  return notificationId !== null;
};

/**
 * Format time ago for notifications
 */
export const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60)
  );

  if (diffInMinutes < 1) return "Zojuist";
  if (diffInMinutes < 60) return `${diffInMinutes} min geleden`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} uur geleden`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7)
    return `${diffInDays} dag${diffInDays > 1 ? "en" : ""} geleden`;

  return date.toLocaleDateString("nl-NL");
};

/**
 * Get notification icon based on type
 */
export const getNotificationIcon = (
  type: "info" | "success" | "warning" | "error"
): string => {
  switch (type) {
    case "success":
      return "✅";
    case "warning":
      return "⚠️";
    case "error":
      return "❌";
    default:
      return "ℹ️";
  }
};

/**
 * Enhanced notification system that sends both email and in-app notifications
 */
export interface EnhancedNotificationParams {
  userId: string;
  userEmail?: string;
  userPhone?: string;
  title: string;
  message: string;
  type?: "info" | "success" | "warning" | "error";
  actionUrl?: string;
  metadata?: Record<string, any>;
  sendEmail?: boolean;
  sendSMS?: boolean;
  emailSubject?: string;
  emailContent?: string;
  smsMessage?: string;
}

/**
 * Send comprehensive notification (in-app + email + SMS)
 */
export const sendEnhancedNotification = async (
  params: EnhancedNotificationParams
): Promise<{
  inAppSuccess: boolean;
  emailSuccess: boolean;
  smsSuccess: boolean;
}> => {
  const results = {
    inAppSuccess: false,
    emailSuccess: false,
    smsSuccess: false,
  };

  // 1. Create in-app notification
  try {
    const notificationId = await createNotification({
      userId: params.userId,
      title: params.title,
      message: params.message,
      type: params.type || "info",
      actionUrl: params.actionUrl,
      metadata: params.metadata,
    });
    results.inAppSuccess = notificationId !== null;
  } catch (error) {
    console.error("Failed to create in-app notification:", error);
  }

  // 2. Send email notification if requested and email is provided
  if (params.sendEmail && params.userEmail) {
    try {
      await sendNotificationEmail({
        to: [params.userEmail],
        subject: params.emailSubject || params.title,
        content:
          params.emailContent ||
          `
          <div style="font-family: Arial, sans-serif; color: #333;">
            <h2>${params.title}</h2>
            <p>${params.message}</p>
            ${
              params.actionUrl
                ? `<p><a href="${window.location.origin}${params.actionUrl}" style="color: #0066cc;">Bekijk in app</a></p>`
                : ""
            }
          </div>
        `,
      });
      results.emailSuccess = true;
    } catch (error) {
      console.error("Failed to send email notification:", error);
    }
  }

  // 3. Send SMS notification if requested and phone is provided
  if (params.sendSMS && params.userPhone) {
    try {
      const { error } = await supabase.functions.invoke("send-sms", {
        body: {
          to: params.userPhone,
          message: params.smsMessage || `${params.title}: ${params.message}`,
        },
      });
      results.smsSuccess = !error;
      if (error) {
        console.error("Failed to send SMS notification:", error);
      }
    } catch (error) {
      console.error("Failed to send SMS notification:", error);
    }
  }

  return results;
};

/**
 * Send notification to multiple users with email/SMS support
 */
export const sendBulkEnhancedNotifications = async (
  users: Array<{
    userId: string;
    email?: string;
    phone?: string;
  }>,
  notificationData: {
    title: string;
    message: string;
    type?: "info" | "success" | "warning" | "error";
    actionUrl?: string;
    sendEmail?: boolean;
    sendSMS?: boolean;
    emailSubject?: string;
    emailContent?: string;
    smsMessage?: string;
  }
): Promise<{
  totalUsers: number;
  inAppSuccess: number;
  emailSuccess: number;
  smsSuccess: number;
}> => {
  const results = {
    totalUsers: users.length,
    inAppSuccess: 0,
    emailSuccess: 0,
    smsSuccess: 0,
  };

  const promises = users.map(async (user) => {
    const result = await sendEnhancedNotification({
      userId: user.userId,
      userEmail: user.email,
      userPhone: user.phone,
      ...notificationData,
    });

    if (result.inAppSuccess) results.inAppSuccess++;
    if (result.emailSuccess) results.emailSuccess++;
    if (result.smsSuccess) results.smsSuccess++;
  });

  await Promise.all(promises);
  return results;
};

/**
 * Get notification badge variant for UI
 */
export const getNotificationBadgeVariant = (
  type: "info" | "success" | "warning" | "error"
) => {
  switch (type) {
    case "success":
      return "default";
    case "warning":
      return "secondary";
    case "error":
      return "destructive";
    default:
      return "outline";
  }
};
